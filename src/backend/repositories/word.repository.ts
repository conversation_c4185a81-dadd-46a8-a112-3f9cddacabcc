import type { Language, Prisma, PrismaClient, Word } from '@prisma/client';
import { type BaseRepository, BaseRepositoryImpl } from './base.repository';

// Define the include structure for Word queries with limited examples (default 5)
export const wordInclude = {
	definitions: {
		include: {
			explains: true,
			examples: {
				take: 5, // Tăng từ 3 lên 5 để hiển thị nhiều examples hơn
				orderBy: {
					created_at: 'desc' as const, // Lấy examples mới nhất theo created_at
				},
			},
		},
	},
	WordNetData: true,
};

// Define the include structure for Word queries with all examples
export const wordIncludeAllExamples = {
	definitions: {
		include: {
			explains: true,
			examples: {
				orderBy: {
					created_at: 'desc' as const, // Order all examples by created_at
				},
			},
		},
	},
	WordNetData: true,
};

export interface WordRepository extends BaseRepository<Word> {
	find(query: Record<string, unknown>, limit?: number): Promise<Word[]>;
	searchWords(term: string, language?: Language, limit?: number): Promise<Word[]>;

	findOrCreateWords(terms: string[], language: Language): Promise<Word[]>;
	findWordsByIds(wordIds: string[]): Promise<Word[]>;
	findByTerm(term: string, language: Language): Promise<Word | null>;
	upsert(data: Prisma.WordCreateInput): Promise<Word>;
	createDefinition(data: Prisma.DefinitionCreateInput): Promise<any>;
	addExamplesToDefinition(
		definitionId: string,
		examples: Array<{ EN: string; VI: string }>
	): Promise<void>;

	// New methods for example pagination
	getExamplesByDefinition(
		definitionId: string,
		offset?: number,
		limit?: number
	): Promise<Array<{ id: string; EN: string; VI: string; created_at: Date; updated_at: Date }>>;
	countExamplesByDefinition(definitionId: string): Promise<number>;
	getDefinitionsByWordId(wordId: string): Promise<Array<{ id: string; pos: any; ipa: string }>>;
}

export class WordRepositoryImpl extends BaseRepositoryImpl<Word> implements WordRepository {
	constructor(private readonly prisma: PrismaClient) {
		super(prisma.word);
	}

	override async findById(id: string): Promise<Word | null> {
		const word = await this.prisma.word.findUnique({
			where: { id },
			include: wordInclude,
		});
		return word;
	}

	override async findOne(query: Record<string, unknown>): Promise<Word | null> {
		const word = await this.prisma.word.findFirst({
			where: query,
			include: wordInclude,
		});
		return word;
	}

	override async find(query: Prisma.WordWhereInput, limit?: number): Promise<Word[]> {
		const words = await this.prisma.word.findMany({
			where: query,
			include: wordInclude,
			take: limit,
		});
		return words;
	}

	override async create(data: Prisma.WordCreateInput): Promise<Word> {
		if (!data.term || !data.language) {
			throw new Error('Term and language are required');
		}

		const word = await this.prisma.word.create({
			data,
			include: wordInclude,
		});
		return word;
	}

	async upsert(data: Prisma.WordCreateInput): Promise<Word> {
		if (!data.term || !data.language) {
			throw new Error('Term and language are required');
		}

		const word = await this.prisma.word.upsert({
			where: {
				term_language: {
					term: data.term,
					language: data.language,
				},
			},
			update: {
				// Update fields if needed (excluding term and language which are unique)
				...(data.definitions && { definitions: data.definitions }),
			},
			create: data,
			include: wordInclude,
		});
		return word;
	}

	override async update(id: string, data: Prisma.WordUpdateInput): Promise<Word> {
		const word = await this.prisma.word.update({
			where: { id },
			data,
			include: wordInclude,
		});
		return word;
	}

	override async delete(query: Record<string, unknown>): Promise<void> {
		await this.prisma.word.deleteMany({
			where: query,
		});
	}

	async searchWords(term: string, language?: Language, limit = 10): Promise<Word[]> {
		const words = await this.prisma.word.findMany({
			where: {
				term: {
					contains: term,
					mode: 'insensitive',
				},
				...(language && { language }),
			},
			include: wordInclude,
			take: limit,
		});
		return words;
	}

	async findOrCreateWords(terms: string[], language: Language): Promise<Word[]> {
		const existingWords = await this.prisma.word.findMany({
			where: {
				term: {
					in: terms,
				},
				language,
			},
			include: wordInclude,
		});

		const existingTerms = new Set(existingWords.map((w) => w.term));
		const newTerms = terms.filter((term) => !existingTerms.has(term));

		if (newTerms.length === 0) {
			return existingWords;
		}

		const newWords = await Promise.all(
			newTerms.map((term) =>
				this.prisma.word.upsert({
					where: {
						term_language: {
							term,
							language,
						},
					},
					update: {},
					create: {
						term,
						language,
					},
					include: wordInclude,
				})
			)
		);

		return [...existingWords, ...newWords];
	}

	async findWordsByIds(wordIds: string[]): Promise<Word[]> {
		const words = await this.prisma.word.findMany({
			where: {
				id: {
					in: wordIds,
				},
			},
			include: wordInclude,
		});
		return words;
	}

	async findByTerm(term: string, language: Language): Promise<Word | null> {
		const word = await this.prisma.word.findFirst({
			where: {
				term,
				language,
			},
			include: wordInclude,
		});
		return word;
	}

	async createDefinition(data: Prisma.DefinitionCreateInput): Promise<any> {
		const definition = await this.prisma.definition.create({
			data,
			include: {
				explains: true,
				examples: {
					orderBy: {
						created_at: 'desc',
					},
				},
			},
		});
		return definition;
	}

	async addExamplesToDefinition(
		definitionId: string,
		examples: Array<{ EN: string; VI: string }>
	): Promise<void> {
		// Use upsert to avoid duplicates based on unique constraint
		for (const example of examples) {
			try {
				await this.prisma.example.upsert({
					where: {
						definition_id_EN_VI: {
							definition_id: definitionId,
							EN: example.EN,
							VI: example.VI,
						},
					},
					update: {
						// Update timestamp if example already exists
						updated_at: new Date(),
					},
					create: {
						EN: example.EN,
						VI: example.VI,
						definition_id: definitionId,
					},
				});
			} catch (error) {
				// If upsert fails due to unique constraint, log and continue
				console.warn(
					`Duplicate example detected and skipped: "${example.EN}" / "${example.VI}"`
				);
			}
		}
	}

	async getExamplesByDefinition(
		definitionId: string,
		offset: number = 0,
		limit: number = 3
	): Promise<Array<{ id: string; EN: string; VI: string; created_at: Date; updated_at: Date }>> {
		const examples = await this.prisma.example.findMany({
			where: {
				definition_id: definitionId,
			},
			select: {
				id: true,
				EN: true,
				VI: true,
				created_at: true,
				updated_at: true,
			},
			orderBy: {
				created_at: 'desc', // Order by created_at instead of id
			},
			skip: offset,
			take: limit,
		});
		return examples;
	}

	async countExamplesByDefinition(definitionId: string): Promise<number> {
		const count = await this.prisma.example.count({
			where: {
				definition_id: definitionId,
			},
		});
		return count;
	}

	async getDefinitionsByWordId(
		wordId: string
	): Promise<Array<{ id: string; pos: any; ipa: string }>> {
		const definitions = await this.prisma.definition.findMany({
			where: {
				word_id: wordId,
			},
			select: {
				id: true,
				pos: true,
				ipa: true,
			},
			orderBy: {
				id: 'asc',
			},
		});
		return definitions;
	}
}
